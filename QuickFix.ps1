# Quick Fix for EntityFrameworkCore 9.0.6
Write-Host "Downloading EntityFrameworkCore 9.0.6..." -ForegroundColor Green

# Download and extract Microsoft.EntityFrameworkCore.Abstractions
Invoke-WebRequest -Uri "https://www.nuget.org/api/v2/package/Microsoft.EntityFrameworkCore.Abstractions/9.0.6" -OutFile "abs.zip"
Expand-Archive -Path "abs.zip" -DestinationPath "abs" -Force
Copy-Item "abs\lib\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll" "." -Force
Remove-Item "abs.zip" -Force
Remove-Item "abs" -Recurse -Force
Write-Host "✓ EntityFrameworkCore.Abstractions downloaded"

# Download and extract Microsoft.EntityFrameworkCore.Relational
Invoke-WebRequest -Uri "https://www.nuget.org/api/v2/package/Microsoft.EntityFrameworkCore.Relational/9.0.6" -OutFile "rel.zip"
Expand-Archive -Path "rel.zip" -DestinationPath "rel" -Force
Copy-Item "rel\lib\net8.0\Microsoft.EntityFrameworkCore.Relational.dll" "." -Force
Remove-Item "rel.zip" -Force
Remove-Item "rel" -Recurse -Force
Write-Host "✓ EntityFrameworkCore.Relational downloaded"

# Download and extract Microsoft.EntityFrameworkCore.Sqlite
Invoke-WebRequest -Uri "https://www.nuget.org/api/v2/package/Microsoft.EntityFrameworkCore.Sqlite/9.0.6" -OutFile "sqlite.zip"
Expand-Archive -Path "sqlite.zip" -DestinationPath "sqlite" -Force
Copy-Item "sqlite\lib\net8.0\Microsoft.EntityFrameworkCore.Sqlite.dll" "." -Force
Remove-Item "sqlite.zip" -Force
Remove-Item "sqlite" -Recurse -Force
Write-Host "✓ EntityFrameworkCore.Sqlite downloaded"

# Download and extract Microsoft.Data.Sqlite
Invoke-WebRequest -Uri "https://www.nuget.org/api/v2/package/Microsoft.Data.Sqlite/9.0.6" -OutFile "datasqlite.zip"
Expand-Archive -Path "datasqlite.zip" -DestinationPath "datasqlite" -Force
Copy-Item "datasqlite\lib\net8.0\Microsoft.Data.Sqlite.dll" "." -Force
Remove-Item "datasqlite.zip" -Force
Remove-Item "datasqlite" -Recurse -Force
Write-Host "✓ Microsoft.Data.Sqlite downloaded"

# Clean up
Remove-Item "ef9.zip" -Force -ErrorAction SilentlyContinue
Remove-Item "ef9" -Recurse -Force -ErrorAction SilentlyContinue

# Update runtime config for .NET 9.0
$runtimeConfig = @"
{
  "runtimeOptions": {
    "tfm": "net8.0",
    "rollForward": "LatestMajor",
    "frameworks": [
      {
        "name": "Microsoft.NETCore.App",
        "version": "8.0.0"
      },
      {
        "name": "Microsoft.WindowsDesktop.App",
        "version": "8.0.0"
      }
    ],
    "additionalProbingPaths": [
      "C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|",
      "C:\\Users\\<USER>\\.nuget\\packages",
      "./",
      "./runtimes/win-x64/native",
      "./runtimes/win-x86/native"
    ],
    "configProperties": {
      "System.Reflection.Metadata.MetadataUpdater.IsSupported": false,
      "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false,
      "System.GC.Server": true,
      "System.GC.Concurrent": true,
      "Microsoft.Data.Sqlite.SqliteConnection.DefaultTimeout": "30"
    }
  }
}
"@

$runtimeConfig | Out-File -FilePath "CarDealershipManagement.runtimeconfig.json" -Encoding UTF8 -Force
Write-Host "✓ Runtime config updated for .NET 8.0"

Write-Host ""
Write-Host "All EntityFrameworkCore 9.0.6 libraries downloaded!" -ForegroundColor Green
Write-Host "Now trying to start the application..." -ForegroundColor Yellow

# Try to start the application
try {
    Start-Process -FilePath ".\CarDealershipManagement.exe" -WindowStyle Normal
    Start-Sleep -Seconds 3
    
    $processes = Get-Process -Name "CarDealershipManagement" -ErrorAction SilentlyContinue
    if ($processes) {
        Write-Host "✓ Application started successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "=== LOGIN CREDENTIALS ===" -ForegroundColor Yellow
        Write-Host "Admin:     admin / admin" -ForegroundColor White
        Write-Host "Manager:   manager / manager" -ForegroundColor White
        Write-Host "Sales:     sales1 / sales1" -ForegroundColor White
        Write-Host "Test:      test / test" -ForegroundColor White
        Write-Host "Developer: amrali / amrali" -ForegroundColor White
    } else {
        Write-Host "Application may have exited. Check for errors." -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error starting application: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
