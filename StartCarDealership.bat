@echo off
title Car Dealership Management System
echo.
echo ========================================
echo   Car Dealership Management System
echo ========================================
echo.
echo Starting application...

REM Check if already running
tasklist /fi "imagename eq CarDealershipManagement.exe" 2>nul | find /i "CarDealershipManagement.exe" >nul
if %errorlevel% equ 0 (
    echo Application is already running!
    echo.
    goto :show_credentials
)

REM Start the application
start "" "CarDealershipManagement.exe"

REM Wait a moment
timeout /t 3 /nobreak >nul

REM Check if started successfully
tasklist /fi "imagename eq CarDealershipManagement.exe" 2>nul | find /i "CarDealershipManagement.exe" >nul
if %errorlevel% equ 0 (
    echo ✓ Application started successfully!
) else (
    echo ✗ Application failed to start
    echo Check the Logs folder for error details
    goto :end
)

:show_credentials
echo.
echo ========================================
echo           LOGIN CREDENTIALS
echo ========================================
echo.
echo Admin:     admin / admin
echo Manager:   manager / manager  
echo Sales:     sales1 / sales1
echo Test:      test / test
echo Developer: amrali / amrali
echo.
echo ========================================
echo.
echo The application window should now be visible on your screen.
echo If you don't see it, check your taskbar or try Alt+Tab.
echo.

:end
echo Press any key to exit...
pause >nul
