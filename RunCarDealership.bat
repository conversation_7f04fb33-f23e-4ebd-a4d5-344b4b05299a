@echo off
echo Starting Car Dealership Management System...
echo.

REM Set environment variables
set PATH=%PATH%;%~dp0
set DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false

REM Check if SQLite files exist
if not exist "e_sqlite3.dll" (
    echo Error: e_sqlite3.dll not found!
    pause
    exit /b 1
)

if not exist "runtimes\win-x64\native\e_sqlite3.dll" (
    echo Error: Native SQLite library not found in runtimes folder!
    pause
    exit /b 1
)

echo All required files found.
echo Starting application...
echo.

REM Start the application
start "" "CarDealershipManagement.exe"

REM Wait a moment to check if it started successfully
timeout /t 3 /nobreak >nul

REM Check if process is running
tasklist /fi "imagename eq CarDealershipManagement.exe" 2>nul | find /i "CarDealershipManagement.exe" >nul
if %errorlevel% equ 0 (
    echo Application started successfully!
    echo.
    echo Login credentials:
    echo - Admin: admin / admin
    echo - Manager: manager / manager
    echo - Sales: sales1 / sales1
    echo - Test: test / test
    echo - Developer: amrali / amrali
) else (
    echo Application may have encountered an error.
    echo Check the logs folder for more information.
)

echo.
echo Press any key to exit...
pause >nul
