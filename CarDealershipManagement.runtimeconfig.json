﻿{
  "runtimeOptions": {
    "tfm": "net7.0",
    "rollForward": "LatestMajor",
    "frameworks": [
      {
        "name": "Microsoft.NETCore.App",
        "version": "7.0.0"
      },
      {
        "name": "Microsoft.WindowsDesktop.App",
        "version": "7.0.0"
      }
    ],
    "additionalProbingPaths": [
      "C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|",
      "C:\\Users\\<USER>\\.nuget\\packages",
      "./",
      "./runtimes/win-x64/native",
      "./runtimes/win-x86/native"
    ],
    "configProperties": {
      "System.Reflection.Metadata.MetadataUpdater.IsSupported": false,
      "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false,
      "System.GC.Server": true,
      "System.GC.Concurrent": true,
      "Microsoft.Data.Sqlite.SqliteConnection.DefaultTimeout": "30"
    }
  }
}
