{"runtimeOptions": {"tfm": "net9.0", "rollForward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "frameworks": [{"name": "Microsoft.NETCore.App", "version": "9.0.0"}, {"name": "Microsoft.WindowsDesktop.App", "version": "9.0.0"}], "additionalProbingPaths": ["C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|", "C:\\Users\\<USER>\\.nuget\\packages", "./", "./runtimes/win-x64/native", "./runtimes/win-x86/native"], "configProperties": {"System.Reflection.Metadata.MetadataUpdater.IsSupported": false, "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false, "System.GC.Server": true, "System.GC.Concurrent": true, "Microsoft.Data.Sqlite.SqliteConnection.DefaultTimeout": "30", "Microsoft.EntityFrameworkCore.EnableServiceProviderCaching": false, "Microsoft.EntityFrameworkCore.EnableSensitiveDataLogging": false, "System.Runtime.Loader.UseRidGraph": true, "System.Runtime.Loader.DisableDefaultContext": false}}}