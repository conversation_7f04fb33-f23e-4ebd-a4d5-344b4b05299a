﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETCoreApp,Version=v8.0" />
  </startup>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="lib;runtimes\win-x64\native;runtimes\win-x86\native;." />
      
      <!-- Force load local assemblies -->
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <codeBase version="9.0.6.0" href="Microsoft.EntityFrameworkCore.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <codeBase version="9.0.6.0" href="Microsoft.EntityFrameworkCore.Abstractions.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Relational" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <codeBase version="9.0.6.0" href="Microsoft.EntityFrameworkCore.Relational.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Sqlite" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <codeBase version="9.0.6.0" href="Microsoft.EntityFrameworkCore.Sqlite.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.Sqlite" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <codeBase version="9.0.6.0" href="Microsoft.Data.Sqlite.dll" />
      </dependentAssembly>
      
    </assemblyBinding>
    
    <AppContextSwitchOverrides value="Switch.Microsoft.Data.Sqlite.AllowAmbientTransactions=true;Switch.System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization=false" />
    
    <gcServer enabled="true" />
    <gcConcurrent enabled="true" />
    
    <loadFromRemoteSources enabled="true" />
    
  </runtime>
  
  <appSettings>
    <add key="SQLitePCL.raw.SetProvider" value="SQLitePCL.SQLite3Provider_e_sqlite3" />
    <add key="EntityFramework.DefaultConnectionFactory" value="Microsoft.Data.Sqlite.SqliteConnectionFactory, Microsoft.Data.Sqlite" />
  </appSettings>
  
</configuration>
