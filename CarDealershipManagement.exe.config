﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="runtimes\win-x64\native;runtimes\win-x86\native" />
    </assemblyBinding>
    <AppContextSwitchOverrides value="Switch.Microsoft.Data.Sqlite.AllowAmbientTransactions=true" />
  </runtime>
  <appSettings>
    <add key="SQLitePCL.raw.SetProvider" value="SQLitePCL.SQLite3Provider_e_sqlite3" />
  </appSettings>
</configuration>
