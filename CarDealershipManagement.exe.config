﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETCoreApp,Version=v7.0" />
  </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="runtimes\win-x64\native;runtimes\win-x86\native;." />
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-7.0.20.0" newVersion="7.0.20.0" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.Sqlite" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-7.0.20.0" newVersion="7.0.20.0" />
      </dependentAssembly>
      
    </assemblyBinding>
    
    <AppContextSwitchOverrides value="Switch.Microsoft.Data.Sqlite.AllowAmbientTransactions=true" />
    
  </runtime>
  
  <appSettings>
    <add key="SQLitePCL.raw.SetProvider" value="SQLitePCL.SQLite3Provider_e_sqlite3" />
  </appSettings>
  
</configuration>
