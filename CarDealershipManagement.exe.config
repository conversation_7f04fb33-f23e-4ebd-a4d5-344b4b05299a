﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETCoreApp,Version=v8.0" />
  </startup>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <!-- Multiple probing paths -->
      <probing privatePath="lib;bin;assemblies;runtimes\win-x64\native;runtimes\win-x86\native" />
      
      <!-- Force EntityFrameworkCore loading from local directory -->
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore" 
                          publicKeyToken="adb9793829ddae60" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        <codeBase version="*******" href="Microsoft.EntityFrameworkCore.dll" />
        <codeBase version="*******" href="lib/Microsoft.EntityFrameworkCore.dll" />
        <codeBase version="*******" href="bin/Microsoft.EntityFrameworkCore.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Abstractions" 
                          publicKeyToken="adb9793829ddae60" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        <codeBase version="*******" href="Microsoft.EntityFrameworkCore.Abstractions.dll" />
        <codeBase version="*******" href="lib/Microsoft.EntityFrameworkCore.Abstractions.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Relational" 
                          publicKeyToken="adb9793829ddae60" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        <codeBase version="*******" href="Microsoft.EntityFrameworkCore.Relational.dll" />
        <codeBase version="*******" href="lib/Microsoft.EntityFrameworkCore.Relational.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Sqlite" 
                          publicKeyToken="adb9793829ddae60" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        <codeBase version="*******" href="Microsoft.EntityFrameworkCore.Sqlite.dll" />
        <codeBase version="*******" href="lib/Microsoft.EntityFrameworkCore.Sqlite.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.Sqlite" 
                          publicKeyToken="adb9793829ddae60" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        <codeBase version="*******" href="Microsoft.Data.Sqlite.dll" />
        <codeBase version="*******" href="lib/Microsoft.Data.Sqlite.dll" />
      </dependentAssembly>
      
    </assemblyBinding>
    
    <!-- Enhanced runtime settings -->
    <AppContextSwitchOverrides value="Switch.Microsoft.Data.Sqlite.AllowAmbientTransactions=true;Switch.System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization=false;Switch.System.Runtime.Loader.UseRidGraph=true" />
    
    <gcServer enabled="true" />
    <gcConcurrent enabled="true" />
    <loadFromRemoteSources enabled="true" />
    <developmentMode developerInstallation="true" />
    
  </runtime>
  
  <appSettings>
    <add key="SQLitePCL.raw.SetProvider" value="SQLitePCL.SQLite3Provider_e_sqlite3" />
    <add key="EntityFramework.DefaultConnectionFactory" value="Microsoft.Data.Sqlite.SqliteConnectionFactory, Microsoft.Data.Sqlite" />
    <add key="vs:EnableBrowserLink" value="false" />
  </appSettings>
  
</configuration>
