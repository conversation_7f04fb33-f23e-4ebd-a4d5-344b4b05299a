﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETCoreApp,Version=v9.0" />
  </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="runtimes\win-x64\native;runtimes\win-x86\native;." />
      
      <!-- EntityFrameworkCore bindings -->
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.6.0" newVersion="9.0.6.0" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.6.0" newVersion="9.0.6.0" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Relational" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.6.0" newVersion="9.0.6.0" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Sqlite" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.6.0" newVersion="9.0.6.0" />
      </dependentAssembly>
      
      <!-- SQLite bindings -->
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.Sqlite" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.6.0" newVersion="9.0.6.0" />
      </dependentAssembly>
      
    </assemblyBinding>
    
    <AppContextSwitchOverrides value="Switch.Microsoft.Data.Sqlite.AllowAmbientTransactions=true;Switch.System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization=false" />
    
    <gcServer enabled="true" />
    <gcConcurrent enabled="true" />
    
  </runtime>
  
  <appSettings>
    <add key="SQLitePCL.raw.SetProvider" value="SQLitePCL.SQLite3Provider_e_sqlite3" />
    <add key="EntityFramework.DefaultConnectionFactory" value="Microsoft.Data.Sqlite.SqliteConnectionFactory, Microsoft.Data.Sqlite" />
  </appSettings>
  
</configuration>
