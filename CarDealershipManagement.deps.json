{"runtimeTarget": {"name": ".NETCoreApp,Version=v7.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v7.0": {"CarDealershipManagement/1.0.0": {"dependencies": {"Microsoft.Data.Sqlite": "7.0.0", "SQLitePCLRaw.batteries_v2": "2.1.0"}, "runtime": {"CarDealershipManagement.dll": {}}}, "Microsoft.Data.Sqlite/7.0.0": {"dependencies": {"SQLitePCLRaw.batteries_v2": "2.1.0"}, "runtime": {"lib/net7.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SQLitePCLRaw.batteries_v2/2.1.0": {"dependencies": {"SQLitePCLRaw.core": "2.1.0", "SQLitePCLRaw.provider.e_sqlite3": "2.1.0"}, "runtime": {"lib/net7.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SQLitePCLRaw.core/2.1.0": {"runtime": {"lib/net7.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.0": {"runtime": {"lib/net7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "runtimeTargets": {"runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}}}, "libraries": {"CarDealershipManagement/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Data.Sqlite/7.0.0": {"type": "package", "serviceable": true, "sha512": ""}, "SQLitePCLRaw.batteries_v2/2.1.0": {"type": "package", "serviceable": true, "sha512": ""}, "SQLitePCLRaw.core/2.1.0": {"type": "package", "serviceable": true, "sha512": ""}, "SQLitePCLRaw.provider.e_sqlite3/2.1.0": {"type": "package", "serviceable": true, "sha512": ""}}}