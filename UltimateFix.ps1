# Ultimate Fix for Car Dealership Management System
# This script resolves all .NET and EntityFrameworkCore issues

Write-Host "=== Ultimate Fix for Car Dealership Management System ===" -ForegroundColor Green
Write-Host ""

# Step 1: Check .NET installation
Write-Host "1. Checking .NET installation..." -ForegroundColor Cyan

try {
    $dotnetVersion = dotnet --version
    Write-Host "✓ .NET SDK Version: $dotnetVersion" -ForegroundColor Green
    
    # Check available runtimes
    $runtimes = dotnet --list-runtimes
    Write-Host "Available runtimes:" -ForegroundColor Yellow
    $runtimes | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
    
} catch {
    Write-Host "✗ .NET not found or not properly installed" -ForegroundColor Red
    Write-Host "Please install .NET 9.0 Desktop Runtime from: https://dotnet.microsoft.com/download" -ForegroundColor Yellow
    Read-Host "Press Enter to continue anyway"
}

# Step 2: Set environment variables
Write-Host ""
Write-Host "2. Setting environment variables..." -ForegroundColor Cyan

$env:DOTNET_ROLL_FORWARD = "LatestMajor"
$env:DOTNET_ROLL_FORWARD_ON_NO_CANDIDATE_FX = "2"
$env:DOTNET_SYSTEM_GLOBALIZATION_INVARIANT = "false"
$env:SQLITE_THREADSAFE = "1"

# Add current directory to PATH
$currentPath = Get-Location
if ($env:PATH -notlike "*$currentPath*") {
    $env:PATH += ";$currentPath"
}

Write-Host "✓ Environment variables configured" -ForegroundColor Green

# Step 3: Create comprehensive app.config
Write-Host ""
Write-Host "3. Creating comprehensive application configuration..." -ForegroundColor Cyan

$appConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETCoreApp,Version=v9.0" />
  </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="runtimes\win-x64\native;runtimes\win-x86\native;." />
      
      <!-- EntityFrameworkCore bindings -->
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Relational" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Sqlite" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      
      <!-- SQLite bindings -->
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.Sqlite" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      
    </assemblyBinding>
    
    <AppContextSwitchOverrides value="Switch.Microsoft.Data.Sqlite.AllowAmbientTransactions=true;Switch.System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization=false" />
    
    <gcServer enabled="true" />
    <gcConcurrent enabled="true" />
    
  </runtime>
  
  <appSettings>
    <add key="SQLitePCL.raw.SetProvider" value="SQLitePCL.SQLite3Provider_e_sqlite3" />
    <add key="EntityFramework.DefaultConnectionFactory" value="Microsoft.Data.Sqlite.SqliteConnectionFactory, Microsoft.Data.Sqlite" />
  </appSettings>
  
</configuration>
"@

$appConfig | Out-File -FilePath "CarDealershipManagement.exe.config" -Encoding UTF8 -Force
Write-Host "✓ Application configuration created" -ForegroundColor Green

# Step 4: Create optimized runtime config
Write-Host ""
Write-Host "4. Creating optimized runtime configuration..." -ForegroundColor Cyan

$runtimeConfig = @"
{
  "runtimeOptions": {
    "tfm": "net9.0",
    "rollForward": "LatestMajor",
    "rollForwardOnNoCandidateFx": 2,
    "frameworks": [
      {
        "name": "Microsoft.NETCore.App",
        "version": "9.0.0"
      },
      {
        "name": "Microsoft.WindowsDesktop.App",
        "version": "9.0.0"
      }
    ],
    "additionalProbingPaths": [
      "C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|",
      "C:\\Users\\<USER>\\.nuget\\packages",
      "./",
      "./runtimes/win-x64/native",
      "./runtimes/win-x86/native"
    ],
    "configProperties": {
      "System.Reflection.Metadata.MetadataUpdater.IsSupported": false,
      "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false,
      "System.GC.Server": true,
      "System.GC.Concurrent": true,
      "Microsoft.Data.Sqlite.SqliteConnection.DefaultTimeout": "30",
      "Microsoft.EntityFrameworkCore.EnableServiceProviderCaching": false,
      "Microsoft.EntityFrameworkCore.EnableSensitiveDataLogging": false,
      "System.Runtime.Loader.UseRidGraph": true,
      "System.Runtime.Loader.DisableDefaultContext": false
    }
  }
}
"@

$runtimeConfig | Out-File -FilePath "CarDealershipManagement.runtimeconfig.json" -Encoding UTF8 -Force
Write-Host "✓ Runtime configuration created" -ForegroundColor Green

# Step 5: Verify database schema
Write-Host ""
Write-Host "5. Verifying database schema..." -ForegroundColor Cyan

if (Test-Path "CarDealership.db") {
    try {
        # Check and fix ModifiedDate column
        $result = sqlite3 CarDealership.db "PRAGMA table_info(Users);" 2>$null
        if ($result -notlike "*ModifiedDate*") {
            Write-Host "Adding missing ModifiedDate column..." -ForegroundColor Yellow
            sqlite3 CarDealership.db "ALTER TABLE Users ADD COLUMN ModifiedDate TEXT;" 2>$null
            sqlite3 CarDealership.db "UPDATE Users SET ModifiedDate = datetime('now') WHERE ModifiedDate IS NULL;" 2>$null
        }
        Write-Host "✓ Database schema verified" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Could not verify database schema" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠ Database will be created on first run" -ForegroundColor Yellow
}

# Step 6: Clear error logs
Write-Host ""
Write-Host "6. Clearing previous error logs..." -ForegroundColor Cyan

if (Test-Path "Logs\error_log.txt") {
    Clear-Content "Logs\error_log.txt" -Force
    Write-Host "✓ Error logs cleared" -ForegroundColor Green
}

# Step 7: Start the application
Write-Host ""
Write-Host "7. Starting Car Dealership Management System..." -ForegroundColor Cyan
Write-Host ""

try {
    # Kill any existing instances
    Get-Process -Name "CarDealershipManagement" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 2
    
    # Start the application
    $process = Start-Process -FilePath ".\CarDealershipManagement.exe" -WindowStyle Normal -PassThru
    
    # Wait and check if it started successfully
    Start-Sleep -Seconds 5
    
    $runningProcesses = Get-Process -Name "CarDealershipManagement" -ErrorAction SilentlyContinue
    
    if ($runningProcesses) {
        Write-Host "✓ Application started successfully!" -ForegroundColor Green
        Write-Host "Running processes:" -ForegroundColor Green
        $runningProcesses | ForEach-Object { 
            Write-Host "  Process ID: $($_.Id), Memory: $([math]::Round($_.WorkingSet/1MB, 2)) MB" -ForegroundColor White
        }
        
        Write-Host ""
        Write-Host "=== LOGIN CREDENTIALS ===" -ForegroundColor Yellow
        Write-Host "Admin:     admin / admin" -ForegroundColor White
        Write-Host "Manager:   manager / manager" -ForegroundColor White
        Write-Host "Sales:     sales1 / sales1" -ForegroundColor White
        Write-Host "Test:      test / test" -ForegroundColor White
        Write-Host "Developer: amrali / amrali" -ForegroundColor White
        Write-Host ""
        Write-Host "The application should now be visible on your screen!" -ForegroundColor Green
        
    } else {
        Write-Host "✗ Application failed to start" -ForegroundColor Red
        
        # Check for recent errors
        if (Test-Path "Logs\error_log.txt") {
            $errors = Get-Content "Logs\error_log.txt" -Tail 5
            if ($errors) {
                Write-Host ""
                Write-Host "Recent errors:" -ForegroundColor Red
                $errors | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
            }
        }
        
        Write-Host ""
        Write-Host "Troubleshooting steps:" -ForegroundColor Yellow
        Write-Host "1. Run this script as Administrator" -ForegroundColor White
        Write-Host "2. Install .NET 9.0 Desktop Runtime" -ForegroundColor White
        Write-Host "3. Check Windows Event Viewer for system errors" -ForegroundColor White
        Write-Host "4. Try running: dotnet CarDealershipManagement.dll" -ForegroundColor White
    }
    
} catch {
    Write-Host "✗ Failed to start application: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
