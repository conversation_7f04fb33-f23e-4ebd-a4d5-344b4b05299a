# Car Dealership Management System - SQLite Fix and Run Script
# This script fixes SQLite DLL issues and database schema problems

Write-Host "=== Car Dealership Management System - Fix & Run ===" -ForegroundColor Green
Write-Host ""

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Function to copy SQLite DLL to system directories (if admin)
function Copy-SQLiteToSystem {
    if (Test-Administrator) {
        try {
            Write-Host "Copying SQLite DLL to system directories..." -ForegroundColor Yellow
            Copy-Item "e_sqlite3.dll" "$env:SystemRoot\System32\e_sqlite3.dll" -Force -ErrorAction SilentlyContinue
            Copy-Item "sqlite3.dll" "$env:SystemRoot\System32\sqlite3.dll" -Force -ErrorAction SilentlyContinue
            Write-Host "✓ SQLite DLLs copied to system directories" -ForegroundColor Green
        } catch {
            Write-Host "⚠ Could not copy to system directories (permissions)" -ForegroundColor Yellow
        }
    }
}

# Step 1: Check required files
Write-Host "1. Checking required files..." -ForegroundColor Cyan

$requiredFiles = @(
    "CarDealershipManagement.exe",
    "CarDealershipManagement.dll",
    "e_sqlite3.dll",
    "sqlite3.dll",
    "Microsoft.Data.Sqlite.dll",
    "SQLitePCLRaw.provider.e_sqlite3.dll"
)

$missingFiles = @()
foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        $missingFiles += $file
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "✗ Missing files:" -ForegroundColor Red
    $missingFiles | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
    Write-Host "Please ensure all required files are present." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
} else {
    Write-Host "✓ All required files found" -ForegroundColor Green
}

# Step 2: Setup runtime directories
Write-Host ""
Write-Host "2. Setting up runtime directories..." -ForegroundColor Cyan

$runtimeDirs = @(
    "runtimes\win-x64\native",
    "runtimes\win-x86\native"
)

foreach ($dir in $runtimeDirs) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✓ Created directory: $dir" -ForegroundColor Green
    }
    
    # Copy SQLite DLLs to runtime directories
    Copy-Item "e_sqlite3.dll" "$dir\e_sqlite3.dll" -Force
    Copy-Item "sqlite3.dll" "$dir\sqlite3.dll" -Force
}

Write-Host "✓ Runtime directories configured" -ForegroundColor Green

# Step 3: Update environment variables
Write-Host ""
Write-Host "3. Updating environment variables..." -ForegroundColor Cyan

$currentPath = $env:PATH
$appPath = Get-Location
if ($currentPath -notlike "*$appPath*") {
    $env:PATH += ";$appPath"
    Write-Host "✓ Added application directory to PATH" -ForegroundColor Green
}

# Set SQLite environment variables
$env:SQLITE_THREADSAFE = "1"
$env:SQLITE_ENABLE_FTS3 = "1"
$env:DOTNET_SYSTEM_GLOBALIZATION_INVARIANT = "false"

Write-Host "✓ Environment variables set" -ForegroundColor Green

# Step 4: Copy to system directories (if admin)
Write-Host ""
Write-Host "4. Attempting system-wide SQLite installation..." -ForegroundColor Cyan
Copy-SQLiteToSystem

# Step 5: Fix database schema
Write-Host ""
Write-Host "5. Checking and fixing database schema..." -ForegroundColor Cyan

if (Test-Path "CarDealership.db") {
    try {
        # Check if ModifiedDate column exists in Users table
        $schemaCheck = sqlite3 CarDealership.db "PRAGMA table_info(Users);" 2>$null
        if ($schemaCheck -notlike "*ModifiedDate*") {
            Write-Host "Adding missing ModifiedDate column to Users table..." -ForegroundColor Yellow
            sqlite3 CarDealership.db "ALTER TABLE Users ADD COLUMN ModifiedDate TEXT;" 2>$null
            sqlite3 CarDealership.db "UPDATE Users SET ModifiedDate = datetime('now') WHERE ModifiedDate IS NULL;" 2>$null
            Write-Host "✓ Database schema fixed" -ForegroundColor Green
        } else {
            Write-Host "✓ Database schema is correct" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠ Could not check/fix database schema" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠ Database file not found - will be created on first run" -ForegroundColor Yellow
}

# Step 6: Start the application
Write-Host ""
Write-Host "6. Starting Car Dealership Management System..." -ForegroundColor Cyan
Write-Host ""

try {
    # Start the application
    $process = Start-Process -FilePath ".\CarDealershipManagement.exe" -WindowStyle Normal -PassThru
    
    # Wait a moment to check if it started successfully
    Start-Sleep -Seconds 3
    
    if (-not $process.HasExited) {
        Write-Host "✓ Application started successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "=== LOGIN CREDENTIALS ===" -ForegroundColor Yellow
        Write-Host "Admin:     admin / admin" -ForegroundColor White
        Write-Host "Manager:   manager / manager" -ForegroundColor White
        Write-Host "Sales:     sales1 / sales1" -ForegroundColor White
        Write-Host "Test:      test / test" -ForegroundColor White
        Write-Host "Developer: amrali / amrali" -ForegroundColor White
        Write-Host ""
        Write-Host "The application is now running. Check your screen for the login window." -ForegroundColor Green
    } else {
        Write-Host "✗ Application exited immediately. Check logs for errors." -ForegroundColor Red
        if (Test-Path "Logs\error_log.txt") {
            Write-Host ""
            Write-Host "Recent errors:" -ForegroundColor Yellow
            Get-Content "Logs\error_log.txt" -Tail 10
        }
    }
} catch {
    Write-Host "✗ Failed to start application: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
