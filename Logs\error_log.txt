[2025-08-01 22:22:49] - Exception:
Type: FileNotFoundException
Message: Could not load file or assembly 'Microsoft.EntityFrameworkCore, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60'. The system cannot find the file specified.
Stack Trace:
   at CarDealershipManagement.Program.EnsureDatabaseExists()
   at CarDealershipManagement.Program.<>c.<Main>b__0_2()
   at CarDealershipManagement.Services.ErrorHandlingService.TryExecute(Action action, String operationName, Boolean showErrorToUser)
--------------------------------------------------------------------------------

[2025-08-01 22:24:31] - Exception:
Type: FileNotFoundException
Message: Could not load file or assembly 'Microsoft.EntityFrameworkCore, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60'. The system cannot find the file specified.
Stack Trace:
   at CarDealershipManagement.Program.EnsureDatabaseExists()
   at CarDealershipManagement.Program.<>c.<Main>b__0_2()
   at CarDealershipManagement.Services.ErrorHandlingService.TryExecute(Action action, String operationName, Boolean showErrorToUser)
--------------------------------------------------------------------------------

[2025-08-01 22:25:01] - Exception:
Type: FileNotFoundException
Message: Could not load file or assembly 'Microsoft.EntityFrameworkCore, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60'. The system cannot find the file specified.
Stack Trace:
   at CarDealershipManagement.Program.EnsureDatabaseExists()
   at CarDealershipManagement.Program.<>c.<Main>b__0_2()
   at CarDealershipManagement.Services.ErrorHandlingService.TryExecute(Action action, String operationName, Boolean showErrorToUser)
--------------------------------------------------------------------------------

