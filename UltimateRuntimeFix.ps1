# Ultimate Runtime Fix - Forces EntityFrameworkCore to load correctly
Write-Host "=== Ultimate Runtime Fix for EntityFrameworkCore ===" -ForegroundColor Green
Write-Host ""

# Step 1: Stop any running instances
Write-Host "1. Stopping running instances..." -ForegroundColor Cyan
Get-Process -Name "CarDealershipManagement" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 3
Write-Host "✓ All instances stopped" -ForegroundColor Green

# Step 2: Create comprehensive directory structure
Write-Host ""
Write-Host "2. Creating comprehensive directory structure..." -ForegroundColor Cyan

# Create multiple lib directories for different scenarios
$libDirs = @("lib", "bin", "assemblies")
foreach ($dir in $libDirs) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

# Copy all DLLs to multiple locations
$allDlls = Get-ChildItem "*.dll" | Where-Object { $_.Name -like "*Entity*" -or $_.Name -like "*Data.Sqlite*" -or $_.Name -like "*SQLite*" }

foreach ($dll in $allDlls) {
    foreach ($dir in $libDirs) {
        Copy-Item $dll.FullName "$dir\" -Force
    }
    Write-Host "  ✓ Copied $($dll.Name) to all lib directories" -ForegroundColor Green
}

Write-Host "✓ Directory structure created" -ForegroundColor Green

# Step 3: Create GAC-style registration
Write-Host ""
Write-Host "3. Creating GAC-style assembly registration..." -ForegroundColor Cyan

$assemblyConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETCoreApp,Version=v8.0" />
  </startup>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <!-- Multiple probing paths -->
      <probing privatePath="lib;bin;assemblies;runtimes\win-x64\native;runtimes\win-x86\native" />
      
      <!-- Force EntityFrameworkCore loading from local directory -->
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore" 
                          publicKeyToken="adb9793829ddae60" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        <codeBase version="*******" href="Microsoft.EntityFrameworkCore.dll" />
        <codeBase version="*******" href="lib/Microsoft.EntityFrameworkCore.dll" />
        <codeBase version="*******" href="bin/Microsoft.EntityFrameworkCore.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Abstractions" 
                          publicKeyToken="adb9793829ddae60" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        <codeBase version="*******" href="Microsoft.EntityFrameworkCore.Abstractions.dll" />
        <codeBase version="*******" href="lib/Microsoft.EntityFrameworkCore.Abstractions.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Relational" 
                          publicKeyToken="adb9793829ddae60" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        <codeBase version="*******" href="Microsoft.EntityFrameworkCore.Relational.dll" />
        <codeBase version="*******" href="lib/Microsoft.EntityFrameworkCore.Relational.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Sqlite" 
                          publicKeyToken="adb9793829ddae60" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        <codeBase version="*******" href="Microsoft.EntityFrameworkCore.Sqlite.dll" />
        <codeBase version="*******" href="lib/Microsoft.EntityFrameworkCore.Sqlite.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.Sqlite" 
                          publicKeyToken="adb9793829ddae60" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        <codeBase version="*******" href="Microsoft.Data.Sqlite.dll" />
        <codeBase version="*******" href="lib/Microsoft.Data.Sqlite.dll" />
      </dependentAssembly>
      
    </assemblyBinding>
    
    <!-- Enhanced runtime settings -->
    <AppContextSwitchOverrides value="Switch.Microsoft.Data.Sqlite.AllowAmbientTransactions=true;Switch.System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization=false;Switch.System.Runtime.Loader.UseRidGraph=true" />
    
    <gcServer enabled="true" />
    <gcConcurrent enabled="true" />
    <loadFromRemoteSources enabled="true" />
    <developmentMode developerInstallation="true" />
    
  </runtime>
  
  <appSettings>
    <add key="SQLitePCL.raw.SetProvider" value="SQLitePCL.SQLite3Provider_e_sqlite3" />
    <add key="EntityFramework.DefaultConnectionFactory" value="Microsoft.Data.Sqlite.SqliteConnectionFactory, Microsoft.Data.Sqlite" />
    <add key="vs:EnableBrowserLink" value="false" />
  </appSettings>
  
</configuration>
"@

$assemblyConfig | Out-File -FilePath "CarDealershipManagement.exe.config" -Encoding UTF8 -Force
Write-Host "✓ GAC-style assembly registration created" -ForegroundColor Green

# Step 4: Create enhanced runtime config
Write-Host ""
Write-Host "4. Creating enhanced runtime configuration..." -ForegroundColor Cyan

$runtimeConfig = @"
{
  "runtimeOptions": {
    "tfm": "net8.0",
    "rollForward": "LatestMajor",
    "frameworks": [
      {
        "name": "Microsoft.NETCore.App",
        "version": "8.0.0"
      },
      {
        "name": "Microsoft.WindowsDesktop.App", 
        "version": "8.0.0"
      }
    ],
    "additionalProbingPaths": [
      "C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|",
      "C:\\Users\\<USER>\\.nuget\\packages",
      "./lib",
      "./bin", 
      "./assemblies",
      "./",
      "./runtimes/win-x64/native",
      "./runtimes/win-x86/native"
    ],
    "configProperties": {
      "System.Reflection.Metadata.MetadataUpdater.IsSupported": false,
      "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false,
      "System.GC.Server": true,
      "System.GC.Concurrent": true,
      "Microsoft.Data.Sqlite.SqliteConnection.DefaultTimeout": "30",
      "System.Runtime.Loader.UseRidGraph": true,
      "System.Runtime.Loader.DisableDefaultContext": false,
      "Microsoft.EntityFrameworkCore.EnableServiceProviderCaching": false,
      "Microsoft.EntityFrameworkCore.EnableSensitiveDataLogging": false,
      "System.Reflection.Assembly.DisablePartialTrust": true
    }
  }
}
"@

$runtimeConfig | Out-File -FilePath "CarDealershipManagement.runtimeconfig.json" -Encoding UTF8 -Force
Write-Host "✓ Enhanced runtime configuration created" -ForegroundColor Green

# Step 5: Set maximum environment variables
Write-Host ""
Write-Host "5. Setting maximum environment variables..." -ForegroundColor Cyan

$env:DOTNET_ROLL_FORWARD = "LatestMajor"
$env:DOTNET_SYSTEM_GLOBALIZATION_INVARIANT = "false"
$env:SQLITE_THREADSAFE = "1"
$env:COMPlus_legacyCorruptedStateExceptionsPolicy = "1"
$env:DOTNET_EnableWriteXorExecute = "0"
$env:COMPlus_LoaderOptimization = "1"
$env:DOTNET_ReadyToRun = "0"
$env:DOTNET_TieredCompilation = "1"

# Add all directories to PATH
$currentPath = Get-Location
$pathsToAdd = @("$currentPath", "$currentPath\lib", "$currentPath\bin", "$currentPath\assemblies")
foreach ($pathToAdd in $pathsToAdd) {
    if ($env:PATH -notlike "*$pathToAdd*") {
        $env:PATH = "$pathToAdd;$env:PATH"
    }
}

Write-Host "✓ Maximum environment variables configured" -ForegroundColor Green

# Step 6: Pre-load assemblies using PowerShell
Write-Host ""
Write-Host "6. Pre-loading assemblies..." -ForegroundColor Cyan

try {
    # Try to load EntityFrameworkCore assemblies
    $efCore = [System.Reflection.Assembly]::LoadFrom("$PWD\Microsoft.EntityFrameworkCore.dll")
    Write-Host "  ✓ Pre-loaded Microsoft.EntityFrameworkCore.dll" -ForegroundColor Green
    
    $efAbs = [System.Reflection.Assembly]::LoadFrom("$PWD\Microsoft.EntityFrameworkCore.Abstractions.dll")
    Write-Host "  ✓ Pre-loaded Microsoft.EntityFrameworkCore.Abstractions.dll" -ForegroundColor Green
    
    $efRel = [System.Reflection.Assembly]::LoadFrom("$PWD\Microsoft.EntityFrameworkCore.Relational.dll")
    Write-Host "  ✓ Pre-loaded Microsoft.EntityFrameworkCore.Relational.dll" -ForegroundColor Green
    
    $efSqlite = [System.Reflection.Assembly]::LoadFrom("$PWD\Microsoft.EntityFrameworkCore.Sqlite.dll")
    Write-Host "  ✓ Pre-loaded Microsoft.EntityFrameworkCore.Sqlite.dll" -ForegroundColor Green
    
    Write-Host "✓ All assemblies pre-loaded successfully" -ForegroundColor Green
} catch {
    Write-Host "⚠ Some assemblies could not be pre-loaded: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 7: Clear logs and start with monitoring
Write-Host ""
Write-Host "7. Starting application with comprehensive monitoring..." -ForegroundColor Cyan

if (Test-Path "Logs\error_log.txt") {
    Clear-Content "Logs\error_log.txt" -Force
}

try {
    # Start the application
    $process = Start-Process -FilePath ".\CarDealershipManagement.exe" -WindowStyle Normal -PassThru
    
    Write-Host "Application started with Process ID: $($process.Id)" -ForegroundColor Yellow
    Write-Host "Monitoring for 15 seconds..." -ForegroundColor Yellow
    
    # Extended monitoring for 15 seconds
    $stable = $true
    for ($i = 1; $i -le 15; $i++) {
        Start-Sleep -Seconds 1
        
        if ($process.HasExited) {
            Write-Host "Process exited after $i seconds" -ForegroundColor Red
            $stable = $false
            break
        }
        
        # Check if process is still running
        $runningProcess = Get-Process -Id $process.Id -ErrorAction SilentlyContinue
        if ($runningProcess) {
            $memoryMB = [math]::Round($runningProcess.WorkingSet / 1MB, 2)
            if ($i % 3 -eq 0) {  # Show every 3 seconds
                Write-Host "[$i/15] Process stable - Memory: $memoryMB MB" -ForegroundColor Green
            }
        } else {
            Write-Host "Process lost after $i seconds" -ForegroundColor Red
            $stable = $false
            break
        }
    }
    
    # Final comprehensive check
    $finalCheck = Get-Process -Name "CarDealershipManagement" -ErrorAction SilentlyContinue
    
    if ($finalCheck -and $stable) {
        Write-Host ""
        Write-Host "🎉 APPLICATION IS RUNNING SUCCESSFULLY AND STABLE! 🎉" -ForegroundColor Green
        Write-Host ""
        Write-Host "Final Status:" -ForegroundColor Cyan
        $finalCheck | ForEach-Object {
            $memoryMB = [math]::Round($_.WorkingSet / 1MB, 2)
            Write-Host "  Process ID: $($_.Id)" -ForegroundColor White
            Write-Host "  Memory Usage: $memoryMB MB" -ForegroundColor White
            Write-Host "  Start Time: $($_.StartTime)" -ForegroundColor White
        }
        
        Write-Host ""
        Write-Host "=== LOGIN CREDENTIALS ===" -ForegroundColor Yellow
        Write-Host "Admin:     admin / admin" -ForegroundColor White
        Write-Host "Manager:   manager / manager" -ForegroundColor White
        Write-Host "Sales:     sales1 / sales1" -ForegroundColor White
        Write-Host "Test:      test / test" -ForegroundColor White
        Write-Host "Developer: amrali / amrali" -ForegroundColor White
        Write-Host ""
        Write-Host "🚗 The Car Dealership Management System is ready to use! 🚗" -ForegroundColor Green
        
    } else {
        Write-Host ""
        Write-Host "✗ Application failed to maintain stability" -ForegroundColor Red
        
        # Check for errors
        if (Test-Path "Logs\error_log.txt") {
            $errors = Get-Content "Logs\error_log.txt" -Tail 5
            if ($errors) {
                Write-Host ""
                Write-Host "Recent errors:" -ForegroundColor Red
                $errors | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
            }
        }
    }
    
} catch {
    Write-Host "✗ Failed to start application: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
