# Final Solution for Car Dealership Management System
Write-Host "=== Final Solution - Car Dealership Management System ===" -ForegroundColor Green
Write-Host ""

# Step 1: Verify all required DLLs are present
Write-Host "1. Verifying required libraries..." -ForegroundColor Cyan

$requiredDlls = @(
    "Microsoft.EntityFrameworkCore.dll",
    "Microsoft.EntityFrameworkCore.Abstractions.dll", 
    "Microsoft.EntityFrameworkCore.Relational.dll",
    "Microsoft.EntityFrameworkCore.Sqlite.dll",
    "Microsoft.Data.Sqlite.dll",
    "CarDealershipManagement.dll",
    "CarDealershipManagement.exe"
)

$allPresent = $true
foreach ($dll in $requiredDlls) {
    if (Test-Path $dll) {
        Write-Host "  ✓ $dll" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $dll - MISSING" -ForegroundColor Red
        $allPresent = $false
    }
}

if (-not $allPresent) {
    Write-Host "Some required files are missing. Please ensure all files are present." -ForegroundColor Red
    Read-Host "Press Enter to continue anyway"
}

# Step 2: Create comprehensive runtime configuration
Write-Host ""
Write-Host "2. Creating comprehensive runtime configuration..." -ForegroundColor Cyan

$runtimeConfig = @"
{
  "runtimeOptions": {
    "tfm": "net8.0",
    "rollForward": "LatestMajor",
    "frameworks": [
      {
        "name": "Microsoft.NETCore.App",
        "version": "8.0.0"
      },
      {
        "name": "Microsoft.WindowsDesktop.App", 
        "version": "8.0.0"
      }
    ],
    "additionalProbingPaths": [
      "C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|",
      "C:\\Users\\<USER>\\.nuget\\packages",
      "./",
      "./lib",
      "./runtimes/win-x64/native",
      "./runtimes/win-x86/native"
    ],
    "configProperties": {
      "System.Reflection.Metadata.MetadataUpdater.IsSupported": false,
      "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false,
      "System.GC.Server": true,
      "System.GC.Concurrent": true,
      "Microsoft.Data.Sqlite.SqliteConnection.DefaultTimeout": "30",
      "Microsoft.EntityFrameworkCore.EnableServiceProviderCaching": false,
      "Microsoft.EntityFrameworkCore.EnableSensitiveDataLogging": false,
      "System.Runtime.Loader.UseRidGraph": true
    }
  }
}
"@

$runtimeConfig | Out-File -FilePath "CarDealershipManagement.runtimeconfig.json" -Encoding UTF8 -Force
Write-Host "✓ Runtime configuration created" -ForegroundColor Green

# Step 3: Create comprehensive app.config
Write-Host ""
Write-Host "3. Creating comprehensive application configuration..." -ForegroundColor Cyan

$appConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETCoreApp,Version=v8.0" />
  </startup>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="lib;runtimes\win-x64\native;runtimes\win-x86\native;." />
      
      <!-- Force load local assemblies -->
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <codeBase version="*******" href="Microsoft.EntityFrameworkCore.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <codeBase version="*******" href="Microsoft.EntityFrameworkCore.Abstractions.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Relational" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <codeBase version="*******" href="Microsoft.EntityFrameworkCore.Relational.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Sqlite" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <codeBase version="*******" href="Microsoft.EntityFrameworkCore.Sqlite.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.Sqlite" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <codeBase version="*******" href="Microsoft.Data.Sqlite.dll" />
      </dependentAssembly>
      
    </assemblyBinding>
    
    <AppContextSwitchOverrides value="Switch.Microsoft.Data.Sqlite.AllowAmbientTransactions=true;Switch.System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization=false" />
    
    <gcServer enabled="true" />
    <gcConcurrent enabled="true" />
    
    <loadFromRemoteSources enabled="true" />
    
  </runtime>
  
  <appSettings>
    <add key="SQLitePCL.raw.SetProvider" value="SQLitePCL.SQLite3Provider_e_sqlite3" />
    <add key="EntityFramework.DefaultConnectionFactory" value="Microsoft.Data.Sqlite.SqliteConnectionFactory, Microsoft.Data.Sqlite" />
  </appSettings>
  
</configuration>
"@

$appConfig | Out-File -FilePath "CarDealershipManagement.exe.config" -Encoding UTF8 -Force
Write-Host "✓ Application configuration created" -ForegroundColor Green

# Step 4: Set environment variables
Write-Host ""
Write-Host "4. Setting environment variables..." -ForegroundColor Cyan

$env:DOTNET_ROLL_FORWARD = "LatestMajor"
$env:DOTNET_SYSTEM_GLOBALIZATION_INVARIANT = "false"
$env:SQLITE_THREADSAFE = "1"
$env:COMPlus_legacyCorruptedStateExceptionsPolicy = "1"

# Add current directory to PATH
$currentPath = Get-Location
if ($env:PATH -notlike "*$currentPath*") {
    $env:PATH += ";$currentPath"
}

Write-Host "✓ Environment variables configured" -ForegroundColor Green

# Step 5: Verify database
Write-Host ""
Write-Host "5. Verifying database..." -ForegroundColor Cyan

if (Test-Path "CarDealership.db") {
    try {
        $result = sqlite3 CarDealership.db "PRAGMA table_info(Users);" 2>$null
        if ($result -notlike "*ModifiedDate*") {
            Write-Host "Adding missing ModifiedDate column..." -ForegroundColor Yellow
            sqlite3 CarDealership.db "ALTER TABLE Users ADD COLUMN ModifiedDate TEXT;" 2>$null
            sqlite3 CarDealership.db "UPDATE Users SET ModifiedDate = datetime('now') WHERE ModifiedDate IS NULL;" 2>$null
        }
        Write-Host "✓ Database verified" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Could not verify database" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠ Database will be created on first run" -ForegroundColor Yellow
}

# Step 6: Clear logs
Write-Host ""
Write-Host "6. Clearing error logs..." -ForegroundColor Cyan

if (Test-Path "Logs\error_log.txt") {
    Clear-Content "Logs\error_log.txt" -Force
}
Write-Host "✓ Error logs cleared" -ForegroundColor Green

# Step 7: Start application with multiple methods
Write-Host ""
Write-Host "7. Starting application..." -ForegroundColor Cyan

# Kill any existing instances
Get-Process -Name "CarDealershipManagement" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 2

# Method 1: Try direct execution
Write-Host "Trying direct execution..." -ForegroundColor Yellow
try {
    $process1 = Start-Process -FilePath ".\CarDealershipManagement.exe" -WindowStyle Normal -PassThru
    Start-Sleep -Seconds 3
    
    if (-not $process1.HasExited) {
        Write-Host "✓ Application started successfully with direct execution!" -ForegroundColor Green
        $success = $true
    } else {
        Write-Host "Direct execution failed, trying dotnet..." -ForegroundColor Yellow
        $success = $false
    }
} catch {
    Write-Host "Direct execution failed: $($_.Exception.Message)" -ForegroundColor Red
    $success = $false
}

# Method 2: Try with dotnet if direct failed
if (-not $success) {
    Write-Host "Trying with dotnet..." -ForegroundColor Yellow
    try {
        $process2 = Start-Process -FilePath "dotnet" -ArgumentList "CarDealershipManagement.dll" -WindowStyle Normal -PassThru
        Start-Sleep -Seconds 3
        
        if (-not $process2.HasExited) {
            Write-Host "✓ Application started successfully with dotnet!" -ForegroundColor Green
            $success = $true
        }
    } catch {
        Write-Host "Dotnet execution failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Check final status
$runningProcesses = Get-Process -Name "CarDealershipManagement" -ErrorAction SilentlyContinue

if ($runningProcesses) {
    Write-Host ""
    Write-Host "✓ APPLICATION IS RUNNING!" -ForegroundColor Green
    Write-Host "Running processes:" -ForegroundColor Green
    $runningProcesses | ForEach-Object { 
        Write-Host "  Process ID: $($_.Id), Memory: $([math]::Round($_.WorkingSet/1MB, 2)) MB" -ForegroundColor White
    }
    
    Write-Host ""
    Write-Host "=== LOGIN CREDENTIALS ===" -ForegroundColor Yellow
    Write-Host "Admin:     admin / admin" -ForegroundColor White
    Write-Host "Manager:   manager / manager" -ForegroundColor White
    Write-Host "Sales:     sales1 / sales1" -ForegroundColor White
    Write-Host "Test:      test / test" -ForegroundColor White
    Write-Host "Developer: amrali / amrali" -ForegroundColor White
    Write-Host ""
    Write-Host "The application window should be visible on your screen!" -ForegroundColor Green
    
} else {
    Write-Host ""
    Write-Host "✗ Application failed to start" -ForegroundColor Red
    
    if (Test-Path "Logs\error_log.txt") {
        $errors = Get-Content "Logs\error_log.txt" -Tail 3
        if ($errors) {
            Write-Host ""
            Write-Host "Recent errors:" -ForegroundColor Red
            $errors | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
        }
    }
    
    Write-Host ""
    Write-Host "Final troubleshooting options:" -ForegroundColor Yellow
    Write-Host "1. Run this script as Administrator" -ForegroundColor White
    Write-Host "2. Install .NET 8.0 Desktop Runtime" -ForegroundColor White
    Write-Host "3. Check Windows Event Viewer" -ForegroundColor White
    Write-Host "4. Contact support with error details" -ForegroundColor White
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
