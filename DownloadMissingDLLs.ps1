# Download Missing DLLs for Car Dealership Management System
Write-Host "=== Downloading Missing .NET Libraries ===" -ForegroundColor Green
Write-Host ""

# Function to download NuGet package
function Download-NuGetPackage {
    param(
        [string]$PackageName,
        [string]$Version,
        [string]$Framework = "net7.0"
    )
    
    Write-Host "Downloading $PackageName $Version..." -ForegroundColor Yellow
    
    try {
        $url = "https://www.nuget.org/api/v2/package/$PackageName/$Version"
        $zipFile = "$PackageName.$Version.zip"
        
        Invoke-WebRequest -Uri $url -OutFile $zipFile -ErrorAction Stop
        
        # Extract the package
        $extractPath = "$PackageName.$Version"
        if (Test-Path $extractPath) { Remove-Item $extractPath -Recurse -Force }
        Expand-Archive -Path $zipFile -DestinationPath $extractPath -Force
        
        # Copy DLLs to current directory
        $libPath = "$extractPath\lib\$Framework"
        if (Test-Path $libPath) {
            Get-ChildItem "$libPath\*.dll" | ForEach-Object {
                Copy-Item $_.FullName "." -Force
                Write-Host "  ✓ Copied $($_.Name)" -ForegroundColor Green
            }
        }
        
        # Also check for net8.0 and net9.0
        foreach ($fw in @("net8.0", "net9.0", "netstandard2.0", "netstandard2.1")) {
            $altLibPath = "$extractPath\lib\$fw"
            if (Test-Path $altLibPath) {
                Get-ChildItem "$altLibPath\*.dll" | ForEach-Object {
                    if (-not (Test-Path $_.Name)) {
                        Copy-Item $_.FullName "." -Force
                        Write-Host "  ✓ Copied $($_.Name) from $fw" -ForegroundColor Green
                    }
                }
            }
        }
        
        # Clean up
        Remove-Item $zipFile -Force
        Remove-Item $extractPath -Recurse -Force
        
        return $true
    } catch {
        Write-Host "  ✗ Failed to download $PackageName`: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# List of required packages
$packages = @(
    @{ Name = "Microsoft.EntityFrameworkCore"; Version = "7.0.20" },
    @{ Name = "Microsoft.EntityFrameworkCore.Abstractions"; Version = "7.0.20" },
    @{ Name = "Microsoft.EntityFrameworkCore.Relational"; Version = "7.0.20" },
    @{ Name = "Microsoft.EntityFrameworkCore.Sqlite"; Version = "7.0.20" },
    @{ Name = "Microsoft.Data.Sqlite"; Version = "7.0.20" },
    @{ Name = "SQLitePCLRaw.core"; Version = "2.1.6" },
    @{ Name = "SQLitePCLRaw.provider.e_sqlite3"; Version = "2.1.6" },
    @{ Name = "SQLitePCLRaw.batteries_v2"; Version = "2.1.6" },
    @{ Name = "System.Runtime"; Version = "4.3.1" },
    @{ Name = "System.Collections"; Version = "4.3.0" },
    @{ Name = "System.Linq"; Version = "4.3.0" },
    @{ Name = "System.Threading.Tasks"; Version = "4.3.0" }
)

Write-Host "Starting download of required packages..." -ForegroundColor Cyan
Write-Host ""

$successCount = 0
$totalCount = $packages.Count

foreach ($package in $packages) {
    if (Download-NuGetPackage -PackageName $package.Name -Version $package.Version) {
        $successCount++
    }
    Write-Host ""
}

Write-Host "Download Summary:" -ForegroundColor Cyan
Write-Host "  Successfully downloaded: $successCount/$totalCount packages" -ForegroundColor Green

# Update runtime config to use .NET 7.0
Write-Host ""
Write-Host "Updating runtime configuration for .NET 7.0..." -ForegroundColor Cyan

$runtimeConfig = @"
{
  "runtimeOptions": {
    "tfm": "net7.0",
    "rollForward": "LatestMajor",
    "frameworks": [
      {
        "name": "Microsoft.NETCore.App",
        "version": "7.0.0"
      },
      {
        "name": "Microsoft.WindowsDesktop.App",
        "version": "7.0.0"
      }
    ],
    "additionalProbingPaths": [
      "C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|",
      "C:\\Users\\<USER>\\.nuget\\packages",
      "./",
      "./runtimes/win-x64/native",
      "./runtimes/win-x86/native"
    ],
    "configProperties": {
      "System.Reflection.Metadata.MetadataUpdater.IsSupported": false,
      "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false,
      "System.GC.Server": true,
      "System.GC.Concurrent": true,
      "Microsoft.Data.Sqlite.SqliteConnection.DefaultTimeout": "30"
    }
  }
}
"@

$runtimeConfig | Out-File -FilePath "CarDealershipManagement.runtimeconfig.json" -Encoding UTF8 -Force
Write-Host "✓ Runtime configuration updated for .NET 7.0" -ForegroundColor Green

# Update app.config for .NET 7.0
Write-Host ""
Write-Host "Updating application configuration for .NET 7.0..." -ForegroundColor Cyan

$appConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETCoreApp,Version=v7.0" />
  </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="runtimes\win-x64\native;runtimes\win-x86\native;." />
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.Sqlite" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      
    </assemblyBinding>
    
    <AppContextSwitchOverrides value="Switch.Microsoft.Data.Sqlite.AllowAmbientTransactions=true" />
    
  </runtime>
  
  <appSettings>
    <add key="SQLitePCL.raw.SetProvider" value="SQLitePCL.SQLite3Provider_e_sqlite3" />
  </appSettings>
  
</configuration>
"@

$appConfig | Out-File -FilePath "CarDealershipManagement.exe.config" -Encoding UTF8 -Force
Write-Host "✓ Application configuration updated for .NET 7.0" -ForegroundColor Green

Write-Host ""
Write-Host "=== Setup Complete ===" -ForegroundColor Green
Write-Host "Now try running the application with:" -ForegroundColor Yellow
Write-Host "  dotnet CarDealershipManagement.dll" -ForegroundColor White
Write-Host "or" -ForegroundColor Yellow
Write-Host "  .\CarDealershipManagement.exe" -ForegroundColor White
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
