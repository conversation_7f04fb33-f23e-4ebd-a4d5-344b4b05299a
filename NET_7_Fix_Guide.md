# دليل حل مشكلة .NET 7.0.0

## المشكلة
```
You must install .NET to run this application.
App: C:\repos\myapp\myapp.exe
Architecture: x64
Host version: 7.0.0
.NET location: Not found
```

## السبب
التطبيق يبحث عن .NET 7.0.0 تحديداً، بينما النظام يحتوي على .NET 7.0.20

## الحل الأمثل

### 1. إنشاء ملف تكوين Runtime
أنشئ ملف `myapp.runtimeconfig.json` بجانب الملف التنفيذي:

```json
{
  "runtimeOptions": {
    "tfm": "net7.0",
    "rollForward": "LatestMajor",
    "applyPatches": true,
    "framework": {
      "name": "Microsoft.NETCore.App",
      "version": "7.0.0"
    },
    "configProperties": {
      "System.Reflection.Metadata.MetadataUpdater.IsSupported": false,
      "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false
    }
  }
}
```

### 2. شرح الإعدادات

- `rollForward: "LatestMajor"`: يسمح باستخدام إصدارات أحدث
- `applyPatches: true`: يطبق التحديثات الأمنية تلقائياً
- `version: "7.0.0"`: الإصدار المطلوب أصلاً
- `configProperties`: إعدادات إضافية للأمان والأداء

### 3. التحقق من النتيجة
```bash
.\myapp.exe
# يجب أن يعمل التطبيق بدون أخطاء
```

## الحلول البديلة

### الحل 1: تثبيت .NET 7.0.0 (غير مستحسن)
```bash
winget install Microsoft.DotNet.Runtime.7 --version 7.0.0
```

### الحل 2: إعادة بناء التطبيق
```bash
dotnet build --framework net7.0
```

## التحقق من الإصدارات المثبتة
```bash
dotnet --list-runtimes
dotnet --info
```

## ملاحظات مهمة

1. **الأمان**: .NET 7.0.0 لم يعد مدعوماً أمنياً
2. **الأداء**: الإصدارات الأحدث تحتوي على تحسينات
3. **التوافق**: ملف التكوين يضمن التوافق مع الإصدارات الأحدث

## استكشاف الأخطاء

### إذا لم يعمل الحل:
1. تأكد من وجود ملف `.runtimeconfig.json` في نفس مجلد الـ `.exe`
2. تحقق من صحة تنسيق JSON
3. تأكد من تثبيت .NET 7.x على النظام
4. جرب تشغيل PowerShell كمدير

### رسائل خطأ شائعة:
- "Framework not found": تحقق من تثبيت .NET Runtime
- "Access denied": شغل كمدير أو غير مجلد التطبيق
- "Invalid JSON": تحقق من تنسيق ملف التكوين

## الخلاصة
هذا الحل يضمن تشغيل التطبيقات التي تتطلب .NET 7.0.0 على الأنظمة التي تحتوي على إصدارات أحدث، مع الحفاظ على الأمان والأداء.
