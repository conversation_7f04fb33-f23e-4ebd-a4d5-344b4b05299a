# Final SQLite Fix for Car Dealership Management System
Write-Host "=== Final SQLite Fix for Car Dealership Management ===" -ForegroundColor Green
Write-Host ""

# Step 1: Download and install Microsoft Visual C++ Redistributable
Write-Host "1. Checking Visual C++ Redistributable..." -ForegroundColor Cyan

try {
    # Check if VC++ Redist is installed
    $vcRedist = Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64" -ErrorAction SilentlyContinue
    if (-not $vcRedist) {
        Write-Host "Downloading Visual C++ Redistributable..." -ForegroundColor Yellow
        $vcRedistUrl = "https://aka.ms/vs/17/release/vc_redist.x64.exe"
        $vcRedistPath = "vc_redist.x64.exe"
        
        Invoke-WebRequest -Uri $vcRedistUrl -OutFile $vcRedistPath
        Write-Host "Installing Visual C++ Redistributable..." -ForegroundColor Yellow
        Start-Process -FilePath $vcRedistPath -ArgumentList "/quiet" -Wait
        Remove-Item $vcRedistPath -Force
        Write-Host "✓ Visual C++ Redistributable installed" -ForegroundColor Green
    } else {
        Write-Host "✓ Visual C++ Redistributable already installed" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Could not install VC++ Redistributable automatically" -ForegroundColor Yellow
}

# Step 2: Download correct SQLite native libraries
Write-Host ""
Write-Host "2. Setting up SQLite native libraries..." -ForegroundColor Cyan

# Create temp directory
$tempDir = "temp_sqlite"
if (Test-Path $tempDir) { Remove-Item $tempDir -Recurse -Force }
New-Item -ItemType Directory -Path $tempDir | Out-Null

try {
    # Download SQLite bundle with native libraries
    $sqliteUrl = "https://www.nuget.org/api/v2/package/SQLitePCLRaw.bundle_e_sqlite3/2.1.6"
    $sqliteZip = "$tempDir\sqlite_bundle.zip"
    
    Write-Host "Downloading SQLite bundle..." -ForegroundColor Yellow
    Invoke-WebRequest -Uri $sqliteUrl -OutFile $sqliteZip
    
    # Extract the package
    Expand-Archive -Path $sqliteZip -DestinationPath $tempDir -Force
    
    # Copy native libraries to correct locations
    $nativeLibs = @(
        "$tempDir\runtimes\win-x64\native\e_sqlite3.dll",
        "$tempDir\runtimes\win-x86\native\e_sqlite3.dll"
    )
    
    foreach ($lib in $nativeLibs) {
        if (Test-Path $lib) {
            $arch = if ($lib -like "*x64*") { "win-x64" } else { "win-x86" }
            $destDir = "runtimes\$arch\native"
            
            if (-not (Test-Path $destDir)) {
                New-Item -ItemType Directory -Path $destDir -Force | Out-Null
            }
            
            Copy-Item $lib "$destDir\e_sqlite3.dll" -Force
            Copy-Item $lib "e_sqlite3.dll" -Force  # Also copy to root
            Write-Host "✓ Copied native library for $arch" -ForegroundColor Green
        }
    }
    
    # Clean up
    Remove-Item $tempDir -Recurse -Force
    Write-Host "✓ SQLite native libraries configured" -ForegroundColor Green
    
} catch {
    Write-Host "⚠ Could not download SQLite bundle: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "Using existing SQLite files..." -ForegroundColor Yellow
}

# Step 3: Create app.config to force correct SQLite loading
Write-Host ""
Write-Host "3. Creating application configuration..." -ForegroundColor Cyan

$appConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="runtimes\win-x64\native;runtimes\win-x86\native" />
    </assemblyBinding>
    <AppContextSwitchOverrides value="Switch.Microsoft.Data.Sqlite.AllowAmbientTransactions=true" />
  </runtime>
  <appSettings>
    <add key="SQLitePCL.raw.SetProvider" value="SQLitePCL.SQLite3Provider_e_sqlite3" />
  </appSettings>
</configuration>
"@

$appConfig | Out-File -FilePath "CarDealershipManagement.exe.config" -Encoding UTF8
Write-Host "✓ Application configuration created" -ForegroundColor Green

# Step 4: Set environment variables for SQLite
Write-Host ""
Write-Host "4. Setting SQLite environment variables..." -ForegroundColor Cyan

$env:SQLITE_THREADSAFE = "1"
$env:SQLITE_ENABLE_FTS3 = "1"
$env:SQLITE_ENABLE_RTREE = "1"
$env:DOTNET_SYSTEM_GLOBALIZATION_INVARIANT = "false"

# Add current directory to PATH
$currentPath = Get-Location
if ($env:PATH -notlike "*$currentPath*") {
    $env:PATH += ";$currentPath"
}

Write-Host "✓ Environment variables configured" -ForegroundColor Green

# Step 5: Initialize SQLite manually
Write-Host ""
Write-Host "5. Initializing SQLite..." -ForegroundColor Cyan

try {
    # Create a simple test to ensure SQLite works
    Add-Type -AssemblyName System.Data.SQLite -ErrorAction SilentlyContinue
    Write-Host "✓ SQLite initialized successfully" -ForegroundColor Green
} catch {
    Write-Host "⚠ SQLite initialization warning (may still work)" -ForegroundColor Yellow
}

# Step 6: Fix database schema if needed
Write-Host ""
Write-Host "6. Verifying database schema..." -ForegroundColor Cyan

if (Test-Path "CarDealership.db") {
    try {
        # Verify ModifiedDate column exists
        $result = sqlite3 CarDealership.db "PRAGMA table_info(Users);" 2>$null
        if ($result -notlike "*ModifiedDate*") {
            Write-Host "Adding missing ModifiedDate column..." -ForegroundColor Yellow
            sqlite3 CarDealership.db "ALTER TABLE Users ADD COLUMN ModifiedDate TEXT;" 2>$null
            sqlite3 CarDealership.db "UPDATE Users SET ModifiedDate = datetime('now') WHERE ModifiedDate IS NULL;" 2>$null
        }
        Write-Host "✓ Database schema verified" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Could not verify database schema" -ForegroundColor Yellow
    }
}

# Step 7: Start the application
Write-Host ""
Write-Host "7. Starting Car Dealership Management System..." -ForegroundColor Cyan
Write-Host ""

try {
    # Clear any previous error logs
    if (Test-Path "Logs\error_log.txt") {
        Clear-Content "Logs\error_log.txt"
    }
    
    # Start the application with special parameters
    $startInfo = New-Object System.Diagnostics.ProcessStartInfo
    $startInfo.FileName = ".\CarDealershipManagement.exe"
    $startInfo.UseShellExecute = $true
    $startInfo.WindowStyle = [System.Diagnostics.ProcessWindowStyle]::Normal
    
    $process = [System.Diagnostics.Process]::Start($startInfo)
    
    # Wait a moment to check if it started
    Start-Sleep -Seconds 5
    
    if (-not $process.HasExited) {
        Write-Host "✓ Application started successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "=== LOGIN CREDENTIALS ===" -ForegroundColor Yellow
        Write-Host "Admin:     admin / admin" -ForegroundColor White
        Write-Host "Manager:   manager / manager" -ForegroundColor White
        Write-Host "Sales:     sales1 / sales1" -ForegroundColor White
        Write-Host "Test:      test / test" -ForegroundColor White
        Write-Host "Developer: amrali / amrali" -ForegroundColor White
        Write-Host ""
        Write-Host "The application should now be running on your screen!" -ForegroundColor Green
        Write-Host "If you still see errors, try running as Administrator." -ForegroundColor Yellow
    } else {
        Write-Host "✗ Application exited. Checking for errors..." -ForegroundColor Red
        if (Test-Path "Logs\error_log.txt") {
            $errors = Get-Content "Logs\error_log.txt" -Tail 5
            if ($errors) {
                Write-Host "Recent errors:" -ForegroundColor Red
                $errors | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
            }
        }
        Write-Host ""
        Write-Host "Try the following:" -ForegroundColor Yellow
        Write-Host "1. Run this script as Administrator" -ForegroundColor White
        Write-Host "2. Install .NET 7.0 Desktop Runtime manually" -ForegroundColor White
        Write-Host "3. Check Windows Event Viewer for system errors" -ForegroundColor White
    }
    
} catch {
    Write-Host "✗ Failed to start application: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
