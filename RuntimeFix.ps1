# Runtime Fix for EntityFrameworkCore Loading Issue
Write-Host "=== Runtime Fix for EntityFrameworkCore ===" -ForegroundColor Green
Write-Host ""

# Step 1: Kill any running instances
Write-Host "1. Stopping any running instances..." -ForegroundColor Cyan
Get-Process -Name "CarDealershipManagement" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 2
Write-Host "✓ Instances stopped" -ForegroundColor Green

# Step 2: Create lib directory and copy all DLLs there
Write-Host ""
Write-Host "2. Creating lib directory structure..." -ForegroundColor Cyan

if (-not (Test-Path "lib")) {
    New-Item -ItemType Directory -Path "lib" -Force | Out-Null
}

# Copy all EntityFrameworkCore DLLs to lib directory
$efDlls = Get-ChildItem "Microsoft.EntityFrameworkCore*.dll"
foreach ($dll in $efDlls) {
    Copy-Item $dll.FullName "lib\" -Force
    Write-Host "  ✓ Copied $($dll.Name) to lib/" -ForegroundColor Green
}

# Copy Microsoft.Data.Sqlite.dll
Copy-Item "Microsoft.Data.Sqlite.dll" "lib\" -Force
Write-Host "  ✓ Copied Microsoft.Data.Sqlite.dll to lib/" -ForegroundColor Green

# Copy SQLite DLLs
Get-ChildItem "SQLite*.dll" | ForEach-Object {
    Copy-Item $_.FullName "lib\" -Force
    Write-Host "  ✓ Copied $($_.Name) to lib/" -ForegroundColor Green
}

Write-Host "✓ Library structure created" -ForegroundColor Green

# Step 3: Create binding redirect configuration
Write-Host ""
Write-Host "3. Creating binding redirect configuration..." -ForegroundColor Cyan

$appConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETCoreApp,Version=v8.0" />
  </startup>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="lib;runtimes\win-x64\native;runtimes\win-x86\native" />
      
      <!-- EntityFrameworkCore Assembly Redirects -->
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore" 
                          publicKeyToken="adb9793829ddae60" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        <codeBase version="*******" href="lib/Microsoft.EntityFrameworkCore.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Abstractions" 
                          publicKeyToken="adb9793829ddae60" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        <codeBase version="*******" href="lib/Microsoft.EntityFrameworkCore.Abstractions.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Relational" 
                          publicKeyToken="adb9793829ddae60" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        <codeBase version="*******" href="lib/Microsoft.EntityFrameworkCore.Relational.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.EntityFrameworkCore.Sqlite" 
                          publicKeyToken="adb9793829ddae60" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        <codeBase version="*******" href="lib/Microsoft.EntityFrameworkCore.Sqlite.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.Sqlite" 
                          publicKeyToken="adb9793829ddae60" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        <codeBase version="*******" href="lib/Microsoft.Data.Sqlite.dll" />
      </dependentAssembly>
      
    </assemblyBinding>
    
    <!-- Runtime Settings -->
    <AppContextSwitchOverrides value="Switch.Microsoft.Data.Sqlite.AllowAmbientTransactions=true;Switch.System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization=false" />
    
    <gcServer enabled="true" />
    <gcConcurrent enabled="true" />
    <loadFromRemoteSources enabled="true" />
    
  </runtime>
  
  <appSettings>
    <add key="SQLitePCL.raw.SetProvider" value="SQLitePCL.SQLite3Provider_e_sqlite3" />
    <add key="EntityFramework.DefaultConnectionFactory" value="Microsoft.Data.Sqlite.SqliteConnectionFactory, Microsoft.Data.Sqlite" />
  </appSettings>
  
</configuration>
"@

$appConfig | Out-File -FilePath "CarDealershipManagement.exe.config" -Encoding UTF8 -Force
Write-Host "✓ Binding redirect configuration created" -ForegroundColor Green

# Step 4: Update runtime config with lib path
Write-Host ""
Write-Host "4. Updating runtime configuration..." -ForegroundColor Cyan

$runtimeConfig = @"
{
  "runtimeOptions": {
    "tfm": "net8.0",
    "rollForward": "LatestMajor",
    "frameworks": [
      {
        "name": "Microsoft.NETCore.App",
        "version": "8.0.0"
      },
      {
        "name": "Microsoft.WindowsDesktop.App", 
        "version": "8.0.0"
      }
    ],
    "additionalProbingPaths": [
      "C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|",
      "C:\\Users\\<USER>\\.nuget\\packages",
      "./lib",
      "./",
      "./runtimes/win-x64/native",
      "./runtimes/win-x86/native"
    ],
    "configProperties": {
      "System.Reflection.Metadata.MetadataUpdater.IsSupported": false,
      "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false,
      "System.GC.Server": true,
      "System.GC.Concurrent": true,
      "Microsoft.Data.Sqlite.SqliteConnection.DefaultTimeout": "30",
      "System.Runtime.Loader.UseRidGraph": true,
      "System.Runtime.Loader.DisableDefaultContext": false
    }
  }
}
"@

$runtimeConfig | Out-File -FilePath "CarDealershipManagement.runtimeconfig.json" -Encoding UTF8 -Force
Write-Host "✓ Runtime configuration updated" -ForegroundColor Green

# Step 5: Set comprehensive environment variables
Write-Host ""
Write-Host "5. Setting comprehensive environment variables..." -ForegroundColor Cyan

$env:DOTNET_ROLL_FORWARD = "LatestMajor"
$env:DOTNET_SYSTEM_GLOBALIZATION_INVARIANT = "false"
$env:SQLITE_THREADSAFE = "1"
$env:COMPlus_legacyCorruptedStateExceptionsPolicy = "1"
$env:DOTNET_EnableWriteXorExecute = "0"

# Add lib directory to PATH
$currentPath = Get-Location
$libPath = Join-Path $currentPath "lib"
if ($env:PATH -notlike "*$libPath*") {
    $env:PATH = "$libPath;$env:PATH"
}
if ($env:PATH -notlike "*$currentPath*") {
    $env:PATH += ";$currentPath"
}

Write-Host "✓ Environment variables configured" -ForegroundColor Green

# Step 6: Clear logs and prepare for fresh start
Write-Host ""
Write-Host "6. Preparing for fresh start..." -ForegroundColor Cyan

if (Test-Path "Logs\error_log.txt") {
    Clear-Content "Logs\error_log.txt" -Force
}

Write-Host "✓ Logs cleared" -ForegroundColor Green

# Step 7: Start application with enhanced monitoring
Write-Host ""
Write-Host "7. Starting application with enhanced monitoring..." -ForegroundColor Cyan

try {
    # Start the application
    $process = Start-Process -FilePath ".\CarDealershipManagement.exe" -WindowStyle Normal -PassThru
    
    Write-Host "Application started with Process ID: $($process.Id)" -ForegroundColor Yellow
    
    # Monitor for 10 seconds
    for ($i = 1; $i -le 10; $i++) {
        Start-Sleep -Seconds 1
        
        if ($process.HasExited) {
            Write-Host "Process exited after $i seconds" -ForegroundColor Red
            break
        }
        
        # Check if process is still running
        $runningProcess = Get-Process -Id $process.Id -ErrorAction SilentlyContinue
        if ($runningProcess) {
            $memoryMB = [math]::Round($runningProcess.WorkingSet / 1MB, 2)
            Write-Host "[$i/10] Process running - Memory: $memoryMB MB" -ForegroundColor Green
        }
    }
    
    # Final check
    $finalCheck = Get-Process -Name "CarDealershipManagement" -ErrorAction SilentlyContinue
    
    if ($finalCheck) {
        Write-Host ""
        Write-Host "✓ APPLICATION IS RUNNING SUCCESSFULLY!" -ForegroundColor Green
        Write-Host ""
        Write-Host "=== LOGIN CREDENTIALS ===" -ForegroundColor Yellow
        Write-Host "Admin:     admin / admin" -ForegroundColor White
        Write-Host "Manager:   manager / manager" -ForegroundColor White
        Write-Host "Sales:     sales1 / sales1" -ForegroundColor White
        Write-Host "Test:      test / test" -ForegroundColor White
        Write-Host "Developer: amrali / amrali" -ForegroundColor White
        Write-Host ""
        Write-Host "The application window should be visible on your screen!" -ForegroundColor Green
        
    } else {
        Write-Host ""
        Write-Host "✗ Application failed to start or exited" -ForegroundColor Red
        
        # Check for errors
        if (Test-Path "Logs\error_log.txt") {
            $errors = Get-Content "Logs\error_log.txt" -Tail 5
            if ($errors) {
                Write-Host ""
                Write-Host "Recent errors:" -ForegroundColor Red
                $errors | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
            }
        }
    }
    
} catch {
    Write-Host "✗ Failed to start application: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
